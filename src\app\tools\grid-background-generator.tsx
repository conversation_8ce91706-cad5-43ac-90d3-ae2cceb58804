import React, { useState, useCallback, useMemo } from 'react';
import { Copy, Download, RotateCcw, Palette, Grid, <PERSON>lide<PERSON>, <PERSON>, Code2, <PERSON>, <PERSON> } from 'lucide-react';

interface GridConfig {
  size: number;
  color: string;
  thickness: number;
  opacity: number;
  backgroundColor: string;
  type: 'lines' | 'dots' | 'crosses';
  offset: number;
  maskEnabled: boolean;
  maskType: 'radial' | 'linear' | 'ellipse' | 'circle' | 'polygon';
  maskSize: number;
  maskPosition: string;
  maskDirection: number;
  maskFeather: number;
  maskInvert: boolean;
}

const GridPatternGenerator: React.FC = () => {
  const [config, setConfig] = useState<GridConfig>({
    size: 32,
    color: '#e5e7eb',
    thickness: 0.5,
    opacity: 100,
    backgroundColor: '#ffffff',
    type: 'lines',
    offset: 0,
    maskEnabled: false,
    maskType: 'radial',
    maskSize: 50,
    maskPosition: 'center',
    maskDirection: 0,
    maskFeather: 20,
    maskInvert: false,
  });

  const [copied, setCopied] = useState(false);
  const [copiedTailwind, setCopiedTailwind] = useState(false);
  const [activeTab, setActiveTab] = useState<'css' | 'tailwind'>('css');
  const [isDarkMode, setIsDarkMode] = useState(false);

  const updateConfig = useCallback((key: keyof GridConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  }, []);

  const resetConfig = useCallback(() => {
    setConfig({
      size: 32,
      color: '#e5e7eb',
      thickness: 0.5,
      opacity: 100,
      backgroundColor: '#ffffff',
      type: 'lines',
      offset: 0,
      maskEnabled: false,
      maskType: 'radial',
      maskSize: 50,
      maskPosition: 'center',
      maskDirection: 0,
      maskFeather: 20,
      maskInvert: false,
    });
  }, []);

  const generateMask = useMemo(() => {
    if (!config.maskEnabled) return {};

    const { maskType, maskSize, maskPosition, maskDirection, maskFeather, maskInvert } = config;
    const featherPercent = maskFeather;
    const sizePercent = maskSize;
    
    let maskImage = '';
    
    switch (maskType) {
      case 'radial':
        maskImage = `radial-gradient(circle at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'linear':
        maskImage = `linear-gradient(${maskDirection}deg, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'ellipse':
        maskImage = `radial-gradient(ellipse at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'circle':
        maskImage = `radial-gradient(circle ${sizePercent}% at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${100 - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} 100%)`;
        break;
      case 'polygon':
        // Create a diamond/rhombus shape using conic gradient
        maskImage = `conic-gradient(from ${maskDirection}deg at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} 0deg, ${maskInvert ? 'transparent' : 'black'} ${90 - featherPercent}deg, ${maskInvert ? 'black' : 'transparent'} 90deg, ${maskInvert ? 'black' : 'transparent'} ${180 - featherPercent}deg, ${maskInvert ? 'transparent' : 'black'} 180deg, ${maskInvert ? 'transparent' : 'black'} ${270 - featherPercent}deg, ${maskInvert ? 'black' : 'transparent'} 270deg, ${maskInvert ? 'black' : 'transparent'} ${360 - featherPercent}deg, ${maskInvert ? 'transparent' : 'black'} 360deg)`;
        break;
    }

    return {
      WebkitMask: maskImage,
      mask: maskImage,
    };
  }, [config]);

  const generateGridPattern = useMemo(() => {
    const { size, color, thickness, opacity, type, offset } = config;
    const adjustedOpacity = opacity / 100;

    // Use dark mode colors if dark mode is enabled and using default colors
    const effectiveColor = isDarkMode && color === '#e5e7eb' ? '#374151' : color;
    const strokeColor = `${effectiveColor}${Math.round(adjustedOpacity * 255).toString(16).padStart(2, '0')}`;

    switch (type) {
      case 'dots':
        return {
          backgroundImage: `radial-gradient(circle, ${strokeColor} ${thickness}px, transparent ${thickness}px)`,
          backgroundSize: `${size}px ${size}px`,
          backgroundPosition: `${offset}px ${offset}px`,
        };
      case 'crosses':
        return {
          backgroundImage: `
            linear-gradient(${strokeColor} ${thickness}px, transparent ${thickness}px),
            linear-gradient(90deg, ${strokeColor} ${thickness}px, transparent ${thickness}px)
          `,
          backgroundSize: `${size}px ${size}px`,
          backgroundPosition: `${offset}px ${offset}px`,
        };
      default: // lines
        return {
          backgroundImage: `
            linear-gradient(${strokeColor} ${thickness}px, transparent ${thickness}px),
            linear-gradient(90deg, ${strokeColor} ${thickness}px, transparent ${thickness}px)
          `,
          backgroundSize: `${size}px ${size}px`,
          backgroundPosition: `${offset}px ${offset}px`,
        };
    }
  }, [config, isDarkMode]);

  const generateCSS = useCallback(() => {
    const { size, color, thickness, opacity, backgroundColor, type, offset, maskEnabled, maskType, maskSize, maskPosition, maskDirection, maskFeather, maskInvert } = config;
    const adjustedOpacity = opacity / 100;
    const strokeColor = `${color}${Math.round(adjustedOpacity * 255).toString(16).padStart(2, '0')}`;

    // Generate dark mode variants
    const darkGridColor = color === '#e5e7eb' ? '#374151' : color;
    const darkBgColor = backgroundColor === '#ffffff' ? '#111827' : backgroundColor;
    const darkStrokeColor = `${darkGridColor}${Math.round(adjustedOpacity * 255).toString(16).padStart(2, '0')}`;

    let backgroundImage = '';
    let darkBackgroundImage = '';

    switch (type) {
      case 'dots':
        backgroundImage = `radial-gradient(circle, ${strokeColor} ${thickness}px, transparent ${thickness}px)`;
        darkBackgroundImage = `radial-gradient(circle, ${darkStrokeColor} ${thickness}px, transparent ${thickness}px)`;
        break;
      case 'crosses':
        backgroundImage = `linear-gradient(${strokeColor} ${thickness}px, transparent ${thickness}px), linear-gradient(90deg, ${strokeColor} ${thickness}px, transparent ${thickness}px)`;
        darkBackgroundImage = `linear-gradient(${darkStrokeColor} ${thickness}px, transparent ${thickness}px), linear-gradient(90deg, ${darkStrokeColor} ${thickness}px, transparent ${thickness}px)`;
        break;
      default:
        backgroundImage = `linear-gradient(${strokeColor} ${thickness}px, transparent ${thickness}px), linear-gradient(90deg, ${strokeColor} ${thickness}px, transparent ${thickness}px)`;
        darkBackgroundImage = `linear-gradient(${darkStrokeColor} ${thickness}px, transparent ${thickness}px), linear-gradient(90deg, ${darkStrokeColor} ${thickness}px, transparent ${thickness}px)`;
    }

    if (!maskEnabled) {
      return `.grid-pattern {
  background-color: ${backgroundColor};
  background-image: ${backgroundImage};
  background-size: ${size}px ${size}px;
  background-position: ${offset}px ${offset}px;
}

/* Dark mode variant */
.dark .grid-pattern {
  background-color: ${darkBgColor};
  background-image: ${darkBackgroundImage};
}`;
    }

    // For masked patterns, we need separate layers
    const featherPercent = maskFeather;
    const sizePercent = maskSize;
    let maskImage = '';

    switch (maskType) {
      case 'radial':
        maskImage = `radial-gradient(circle at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'linear':
        maskImage = `linear-gradient(${maskDirection}deg, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'ellipse':
        maskImage = `radial-gradient(ellipse at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'circle':
        maskImage = `radial-gradient(circle ${sizePercent}% at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${100 - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} 100%)`;
        break;
      case 'polygon':
        maskImage = `conic-gradient(from ${maskDirection}deg at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} 0deg, ${maskInvert ? 'transparent' : 'black'} ${90 - featherPercent}deg, ${maskInvert ? 'black' : 'transparent'} 90deg, ${maskInvert ? 'black' : 'transparent'} ${180 - featherPercent}deg, ${maskInvert ? 'transparent' : 'black'} 180deg, ${maskInvert ? 'transparent' : 'black'} ${270 - featherPercent}deg, ${maskInvert ? 'black' : 'transparent'} 270deg, ${maskInvert ? 'black' : 'transparent'} ${360 - featherPercent}deg, ${maskInvert ? 'transparent' : 'black'} 360deg)`;
        break;
    }

    return `.grid-pattern {
  background-color: ${backgroundColor};
  position: relative;
}

.grid-pattern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: ${backgroundImage};
  background-size: ${size}px ${size}px;
  background-position: ${offset}px ${offset}px;
  -webkit-mask: ${maskImage};
  mask: ${maskImage};
}

/* Dark mode variant */
.dark .grid-pattern {
  background-color: ${darkBgColor};
}

.dark .grid-pattern::before {
  background-image: ${darkBackgroundImage};
}`;
  }, [config]);

  const generateTailwindCSS = useCallback(() => {
    const { size, color, thickness, opacity, backgroundColor, type, offset, maskEnabled, maskType, maskSize, maskPosition, maskDirection, maskFeather, maskInvert } = config;

    // Convert hex colors to RGB for Tailwind arbitrary values
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    };

    const bgRgb = hexToRgb(backgroundColor);
    const gridRgb = hexToRgb(color);
    const adjustedOpacity = opacity / 100;

    // Generate dark mode variants
    const darkGridColor = color === '#e5e7eb' ? '#374151' : color;
    const darkBgColor = backgroundColor === '#ffffff' ? '#111827' : backgroundColor;
    const darkGridRgb = hexToRgb(darkGridColor);
    const darkBgRgb = hexToRgb(darkBgColor);

    const bgColorValue = bgRgb ? `rgb(${bgRgb.r} ${bgRgb.g} ${bgRgb.b})` : backgroundColor;
    const gridColorValue = gridRgb ? `rgb(${gridRgb.r} ${gridRgb.g} ${gridRgb.b} / ${adjustedOpacity})` : color;
    const darkBgColorValue = darkBgRgb ? `rgb(${darkBgRgb.r} ${darkBgRgb.g} ${darkBgRgb.b})` : darkBgColor;
    const darkGridColorValue = darkGridRgb ? `rgb(${darkGridRgb.r} ${darkGridRgb.g} ${darkGridRgb.b} / ${adjustedOpacity})` : darkGridColor;
    
    let backgroundImage = '';
    let darkBackgroundImage = '';

    switch (type) {
      case 'dots':
        backgroundImage = `radial-gradient(circle, ${gridColorValue} ${thickness}px, transparent ${thickness}px)`;
        darkBackgroundImage = `radial-gradient(circle, ${darkGridColorValue} ${thickness}px, transparent ${thickness}px)`;
        break;
      case 'crosses':
      case 'lines':
        backgroundImage = `linear-gradient(${gridColorValue} ${thickness}px, transparent ${thickness}px), linear-gradient(90deg, ${gridColorValue} ${thickness}px, transparent ${thickness}px)`;
        darkBackgroundImage = `linear-gradient(${darkGridColorValue} ${thickness}px, transparent ${thickness}px), linear-gradient(90deg, ${darkGridColorValue} ${thickness}px, transparent ${thickness}px)`;
        break;
    }

    if (!maskEnabled) {
      return `<!-- Add this to your HTML element -->
<div class="grid-pattern"></div>

<!-- Add this to your CSS or Tailwind config -->
@layer utilities {
  .grid-pattern {
    background-color: ${bgColorValue};
    background-image: ${backgroundImage};
    background-size: ${size}px ${size}px;
    background-position: ${offset}px ${offset}px;
  }

  /* Dark mode variant */
  .dark .grid-pattern {
    background-color: ${darkBgColorValue};
    background-image: ${darkBackgroundImage};
  }
}

<!-- Or use arbitrary values directly in class (Tailwind v3.1+) -->
<div class="relative dark:bg-gray-900" style="background-color: ${bgColorValue}; background-image: ${backgroundImage}; background-size: ${size}px ${size}px; background-position: ${offset}px ${offset}px;"></div>`;
    }

    // For masked patterns
    const featherPercent = maskFeather;
    const sizePercent = maskSize;
    let maskImage = '';
    
    switch (maskType) {
      case 'radial':
        maskImage = `radial-gradient(circle at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'linear':
        maskImage = `linear-gradient(${maskDirection}deg, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'ellipse':
        maskImage = `radial-gradient(ellipse at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${sizePercent - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} ${sizePercent}%)`;
        break;
      case 'circle':
        maskImage = `radial-gradient(circle ${sizePercent}% at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} ${100 - featherPercent}%, ${maskInvert ? 'black' : 'transparent'} 100%)`;
        break;
      case 'polygon':
        maskImage = `conic-gradient(from ${maskDirection}deg at ${maskPosition}, ${maskInvert ? 'transparent' : 'black'} 0deg, ${maskInvert ? 'transparent' : 'black'} ${90 - featherPercent}deg, ${maskInvert ? 'black' : 'transparent'} 90deg, ${maskInvert ? 'black' : 'transparent'} ${180 - featherPercent}deg, ${maskInvert ? 'transparent' : 'black'} 180deg, ${maskInvert ? 'transparent' : 'black'} ${270 - featherPercent}deg, ${maskInvert ? 'black' : 'transparent'} 270deg, ${maskInvert ? 'black' : 'transparent'} ${360 - featherPercent}deg, ${maskInvert ? 'transparent' : 'black'} 360deg)`;
        break;
    }
    
    return `<!-- Add this to your HTML element -->
<div class="grid-pattern-masked"></div>

<!-- Add this to your CSS or Tailwind config -->
@layer utilities {
  .grid-pattern-masked {
    background-color: ${bgColorValue};
    position: relative;
  }

  .grid-pattern-masked::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: ${backgroundImage};
    background-size: ${size}px ${size}px;
    background-position: ${offset}px ${offset}px;
    -webkit-mask: ${maskImage};
    mask: ${maskImage};
  }

  /* Dark mode variant */
  .dark .grid-pattern-masked {
    background-color: ${darkBgColorValue};
  }

  .dark .grid-pattern-masked::before {
    background-image: ${darkBackgroundImage};
  }
}

<!-- Or use with Tailwind classes -->
<div class="relative dark:bg-gray-900" style="background-color: ${bgColorValue};">
  <div class="absolute inset-0" style="background-image: ${backgroundImage}; background-size: ${size}px ${size}px; background-position: ${offset}px ${offset}px; -webkit-mask: ${maskImage}; mask: ${maskImage};"></div>
</div>`;
  }, [config]);

  const copyCSS = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(generateCSS());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy CSS:', err);
    }
  }, [generateCSS]);

  const copyTailwindCSS = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(generateTailwindCSS());
      setCopiedTailwind(true);
      setTimeout(() => setCopiedTailwind(false), 2000);
    } catch (err) {
      console.error('Failed to copy Tailwind CSS:', err);
    }
  }, [generateTailwindCSS]);

  const downloadCSS = useCallback(() => {
    const css = activeTab === 'css' ? generateCSS() : generateTailwindCSS();
    const filename = activeTab === 'css' ? 'grid-pattern.css' : 'grid-pattern-tailwind.css';
    const blob = new Blob([css], { type: 'text/css' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [generateCSS, generateTailwindCSS, activeTab]);

  const presetPatterns = [
    { name: 'Subtle Grid', config: { size: 32, color: '#f3f4f6', thickness: 0.5, opacity: 50, backgroundColor: '#ffffff', type: 'lines' as const, offset: 0, maskEnabled: false, maskType: 'radial' as const, maskSize: 50, maskPosition: 'center', maskDirection: 0, maskFeather: 20, maskInvert: false } },
    { name: 'Bold Dots', config: { size: 24, color: '#3b82f6', thickness: 2, opacity: 30, backgroundColor: '#f8fafc', type: 'dots' as const, offset: 0, maskEnabled: false, maskType: 'radial' as const, maskSize: 50, maskPosition: 'center', maskDirection: 0, maskFeather: 20, maskInvert: false } },
    { name: 'Dark Theme', config: { size: 28, color: '#374151', thickness: 0.3, opacity: 40, backgroundColor: '#111827', type: 'lines' as const, offset: 0, maskEnabled: true, maskType: 'radial' as const, maskSize: 70, maskPosition: 'center', maskDirection: 0, maskFeather: 25, maskInvert: false } },
    { name: 'Neon Glow', config: { size: 20, color: '#10b981', thickness: 0.8, opacity: 60, backgroundColor: '#0f172a', type: 'dots' as const, offset: 0, maskEnabled: true, maskType: 'circle' as const, maskSize: 45, maskPosition: 'center', maskDirection: 0, maskFeather: 30, maskInvert: false } },
  ];

  const maskPositions = [
    { label: 'Center', value: 'center' },
    { label: 'Top Left', value: 'top left' },
    { label: 'Top', value: 'top' },
    { label: 'Top Right', value: 'top right' },
    { label: 'Right', value: 'right' },
    { label: 'Bottom Right', value: 'bottom right' },
    { label: 'Bottom', value: 'bottom' },
    { label: 'Bottom Left', value: 'bottom left' },
    { label: 'Left', value: 'left' },
  ];

  return (
    <div className={`min-h-screen transition-colors duration-200 ${isDarkMode ? 'dark bg-gradient-to-br from-gray-900 to-gray-800' : 'bg-gradient-to-br from-slate-50 to-slate-100'}`}>
      <div className="flex h-screen">
        {/* Sidebar Controls */}
        <div className={`w-80 border-r overflow-y-auto transition-colors duration-200 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <div className="p-6">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500 rounded-lg">
                  <Grid className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className={`text-xl font-bold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Grid Generator</h1>
                  <p className={`text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Create custom grid patterns</p>
                </div>
              </div>
              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className={`p-2 rounded-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' : 'bg-gray-100 hover:bg-gray-200 text-gray-600'}`}
                title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </button>
            </div>

            {/* Presets */}
            <div className="mb-8">
              <h3 className={`text-sm font-semibold mb-3 flex items-center gap-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                <Palette className="w-4 h-4" />
                Presets
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {presetPatterns.map((preset) => (
                  <button
                    key={preset.name}
                    onClick={() => setConfig(preset.config)}
                    className={`p-3 text-xs font-medium rounded-lg transition-colors text-left ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-200' : 'bg-gray-50 hover:bg-gray-100 text-gray-700'}`}
                  >
                    {preset.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Controls */}
            <div className="space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <Sliders className={`w-4 h-4 transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                <h3 className={`text-sm font-semibold transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Grid Controls</h3>
              </div>

              {/* Grid Type */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Pattern Type</label>
                <div className="flex gap-2">
                  {(['lines', 'dots', 'crosses'] as const).map((type) => (
                    <button
                      key={type}
                      onClick={() => updateConfig('type', type)}
                      className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors capitalize ${
                        config.type === type
                          ? 'bg-blue-500 text-white'
                          : isDarkMode
                            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {type}
                    </button>
                  ))}
                </div>
              </div>

              {/* Grid Size */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Grid Size: {config.size}px
                </label>
                <input
                  type="range"
                  min="8"
                  max="128"
                  value={config.size}
                  onChange={(e) => updateConfig('size', parseInt(e.target.value))}
                  className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                />
              </div>

              {/* Line Thickness */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Line Thickness: {config.thickness}px
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="8"
                  step="0.1"
                  value={config.thickness}
                  onChange={(e) => updateConfig('thickness', parseFloat(e.target.value))}
                  className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                />
                <div className={`flex justify-between text-xs mt-1 transition-colors duration-200 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                  <span>0.1px</span>
                  <span>8px</span>
                </div>
              </div>

              {/* Opacity */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Opacity: {config.opacity}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={config.opacity}
                  onChange={(e) => updateConfig('opacity', parseInt(e.target.value))}
                  className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                />
              </div>

              {/* Offset */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Offset: {config.offset}px
                </label>
                <input
                  type="range"
                  min="-32"
                  max="32"
                  value={config.offset}
                  onChange={(e) => updateConfig('offset', parseInt(e.target.value))}
                  className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                />
              </div>

              {/* Grid Color */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Grid Color</label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={config.color}
                    onChange={(e) => updateConfig('color', e.target.value)}
                    className={`w-12 h-10 rounded-lg border cursor-pointer transition-colors duration-200 ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-white'}`}
                  />
                  <input
                    type="text"
                    value={config.color}
                    onChange={(e) => updateConfig('color', e.target.value)}
                    className={`flex-1 px-3 py-2 border rounded-lg text-sm font-mono transition-colors duration-200 ${isDarkMode ? 'border-gray-600 bg-gray-700 text-gray-200' : 'border-gray-200 bg-white text-gray-900'}`}
                  />
                </div>
              </div>

              {/* Background Color */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Background Color</label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={config.backgroundColor}
                    onChange={(e) => updateConfig('backgroundColor', e.target.value)}
                    className={`w-12 h-10 rounded-lg border cursor-pointer transition-colors duration-200 ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-white'}`}
                  />
                  <input
                    type="text"
                    value={config.backgroundColor}
                    onChange={(e) => updateConfig('backgroundColor', e.target.value)}
                    className={`flex-1 px-3 py-2 border rounded-lg text-sm font-mono transition-colors duration-200 ${isDarkMode ? 'border-gray-600 bg-gray-700 text-gray-200' : 'border-gray-200 bg-white text-gray-900'}`}
                  />
                </div>
              </div>

              {/* Masking Section */}
              <div className={`pt-6 border-t transition-colors duration-200 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                <div className="flex items-center gap-2 mb-4">
                  <Eye className={`w-4 h-4 transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <h3 className={`text-sm font-semibold transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Masking</h3>
                </div>

                {/* Enable Mask */}
                <div className="mb-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={config.maskEnabled}
                      onChange={(e) => updateConfig('maskEnabled', e.target.checked)}
                      className={`w-4 h-4 text-blue-600 rounded focus:ring-blue-500 transition-colors duration-200 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-100 border-gray-300'}`}
                    />
                    <span className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Enable Masking</span>
                  </label>
                </div>

                {config.maskEnabled && (
                  <div className="space-y-4">
                    {/* Mask Type */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Mask Type</label>
                      <select
                        value={config.maskType}
                        onChange={(e) => updateConfig('maskType', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg text-sm transition-colors duration-200 ${isDarkMode ? 'border-gray-600 bg-gray-700 text-gray-200' : 'border-gray-200 bg-white text-gray-900'}`}
                      >
                        <option value="radial">Radial</option>
                        <option value="linear">Linear</option>
                        <option value="ellipse">Ellipse</option>
                        <option value="circle">Circle</option>
                        <option value="polygon">Polygon</option>
                      </select>
                    </div>

                    {/* Mask Size */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Mask Size: {config.maskSize}%
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="100"
                        value={config.maskSize}
                        onChange={(e) => updateConfig('maskSize', parseInt(e.target.value))}
                        className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                      />
                    </div>

                    {/* Mask Position */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Position</label>
                      <select
                        value={config.maskPosition}
                        onChange={(e) => updateConfig('maskPosition', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg text-sm transition-colors duration-200 ${isDarkMode ? 'border-gray-600 bg-gray-700 text-gray-200' : 'border-gray-200 bg-white text-gray-900'}`}
                      >
                        {maskPositions.map((pos) => (
                          <option key={pos.value} value={pos.value}>
                            {pos.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Mask Direction (for linear and polygon) */}
                    {(config.maskType === 'linear' || config.maskType === 'polygon') && (
                      <div>
                        <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Direction: {config.maskDirection}°
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="360"
                          value={config.maskDirection}
                          onChange={(e) => updateConfig('maskDirection', parseInt(e.target.value))}
                          className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                        />
                      </div>
                    )}

                    {/* Mask Feather */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        Feather: {config.maskFeather}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="50"
                        value={config.maskFeather}
                        onChange={(e) => updateConfig('maskFeather', parseInt(e.target.value))}
                        className={`w-full h-2 rounded-lg appearance-none cursor-pointer slider transition-colors duration-200 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}
                      />
                    </div>

                    {/* Invert Mask */}
                    <div>
                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={config.maskInvert}
                          onChange={(e) => updateConfig('maskInvert', e.target.checked)}
                          className={`w-4 h-4 text-blue-600 rounded focus:ring-blue-500 transition-colors duration-200 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-100 border-gray-300'}`}
                        />
                        <span className={`text-sm font-medium transition-colors duration-200 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Invert Mask</span>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className={`mt-8 pt-6 border-t space-y-3 transition-colors duration-200 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
              <button
                onClick={resetConfig}
                className={`w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${isDarkMode ? 'text-gray-300 bg-gray-700 hover:bg-gray-600' : 'text-gray-600 bg-gray-100 hover:bg-gray-200'}`}
              >
                <RotateCcw className="w-4 h-4" />
                Reset
              </button>

              <button
                onClick={copyCSS}
                className={`w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  copied
                    ? 'bg-green-500 text-white'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                <Copy className="w-4 h-4" />
                {copied ? 'Copied!' : 'Copy CSS'}
              </button>

              <button
                onClick={copyTailwindCSS}
                className={`w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  copiedTailwind
                    ? 'bg-green-500 text-white'
                    : 'bg-cyan-500 hover:bg-cyan-600 text-white'
                }`}
              >
                <Code2 className="w-4 h-4" />
                {copiedTailwind ? 'Copied!' : 'Copy Tailwind'}
              </button>

              <button
                onClick={downloadCSS}
                className={`w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium border rounded-lg transition-colors ${isDarkMode ? 'text-gray-300 bg-gray-800 border-gray-600 hover:bg-gray-700' : 'text-gray-700 bg-white border-gray-200 hover:bg-gray-50'}`}
              >
                <Download className="w-4 h-4" />
                Download {activeTab === 'css' ? 'CSS' : 'Tailwind'}
              </button>
            </div>
          </div>
        </div>

        {/* Main Preview Area */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className={`border-b px-6 py-4 transition-colors duration-200 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h2 className={`text-lg font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Live Preview</h2>
            <p className={`text-sm transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>See your grid pattern with masking in real-time</p>
          </div>

          {/* Preview */}
          <div className="flex-1 p-6">
            <div className={`h-full w-full rounded-xl border overflow-hidden shadow-lg transition-colors duration-200 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
              <div
                className="w-full h-full transition-all duration-200 relative"
                style={{ backgroundColor: isDarkMode && config.backgroundColor === '#ffffff' ? '#111827' : config.backgroundColor }}
              >
                {config.maskEnabled ? (
                  <div
                    className="absolute inset-0"
                    style={{
                      ...generateGridPattern,
                      ...generateMask,
                    }}
                  />
                ) : (
                  <div
                    className="absolute inset-0"
                    style={generateGridPattern}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Code Preview */}
          <div className={`text-gray-100 max-h-64 overflow-y-auto transition-colors duration-200 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-800'}`}>
            {/* Tab Headers */}
            <div className={`flex border-b transition-colors duration-200 ${isDarkMode ? 'border-gray-700' : 'border-gray-600'}`}>
              <button
                onClick={() => setActiveTab('css')}
                className={`px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'css'
                    ? `${isDarkMode ? 'bg-gray-800' : 'bg-gray-700'} text-white border-b-2 border-blue-400`
                    : 'text-gray-400 hover:text-gray-200'
                }`}
              >
                CSS
              </button>
              <button
                onClick={() => setActiveTab('tailwind')}
                className={`px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'tailwind'
                    ? `${isDarkMode ? 'bg-gray-800' : 'bg-gray-700'} text-white border-b-2 border-cyan-400`
                    : 'text-gray-400 hover:text-gray-200'
                }`}
              >
                Tailwind CSS
              </button>
            </div>

            {/* Code Content */}
            <div className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-300">
                  Generated {activeTab === 'css' ? 'CSS' : 'Tailwind CSS'}
                </h3>
                <button
                  onClick={activeTab === 'css' ? copyCSS : copyTailwindCSS}
                  className={`text-xs px-3 py-1 rounded transition-colors ${
                    (activeTab === 'css' && copied) || (activeTab === 'tailwind' && copiedTailwind)
                      ? 'bg-green-600 text-white'
                      : `${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-600 hover:bg-gray-500'} text-gray-200`
                  }`}
                >
                  {(activeTab === 'css' && copied) || (activeTab === 'tailwind' && copiedTailwind) ? 'Copied!' : 'Copy'}
                </button>
              </div>
              <pre className="text-sm font-mono leading-relaxed text-gray-200 whitespace-pre-wrap">
                <code>{activeTab === 'css' ? generateCSS() : generateTailwindCSS()}</code>
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GridPatternGenerator;